program solve_permutated_letters;
uses Classes, CastleUnicode;

var
  Words: TStringList;

{ Make sure all words are lowercase. }
procedure LowercaseList(List: TStringList);
var
  I: Integer;
begin
  for I := 0 to List.Count - 1 do
    List[I] := Lowercase(List[I]);
end;

{ Is A permutation of B. }
function IsPermutation(const A, B: String): Boolean;
var
  I: Integer;
begin
  Result := False;
  if Length(A) <> Length(B) then Exit;
  for I := 1 to Length(A) do
    if Pos(A[I], B) = 0 then Exit;
  Result := True;
end;

{ Find word from dictionary matching (by permutation) the given word. }
procedure SolveWord(Word: String);
var
  DictWord: String;
  I: Integer;
begin
  Write('Solving ', Word, ': ');
  Word := Lowercase(Word);

  for DictWord in Words do
    if IsPermutation(Word, DictWord) then
      Write(DictWord, ' ');
  Writeln;
end;

begin
  Words := TStringList.Create;
  try
    Words.LoadFromFile('polish_words.txt');
    LowercaseList(Words);
    Writeln('Loaded ', Words.Count, ' words');
    for I := 1 to ParamCount do
      SolveWord(ParamStr(I));
  finally FreeAndNil(Words) end;
end.